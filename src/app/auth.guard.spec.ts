import { TestBed } from '@angular/core/testing';
import { CanActivateFn, Router } from '@angular/router';
import { authGuard } from './auth.guard';

describe('authGuard', () => {
  let router: jasmine.SpyObj<Router>;
  const executeGuard: CanActivateFn = (...guardParameters) =>
      TestBed.runInInjectionContext(() => authGuard(...guardParameters));

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        { provide: Router, useValue: routerSpy }
      ]
    });

    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });

  it('should allow access when user is authenticated', () => {
    // Arrange
    const mockUser = {
      id: 1,
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      adminsRole: { id: 1, name: 'Admin' }
    };
    localStorage.setItem('user', JSON.stringify(mockUser));

    // Act
    const result = executeGuard({} as any, {} as any);

    // Assert
    expect(result).toBe(true);
  });

  it('should deny access when user is not authenticated', () => {
    // Act
    const result = executeGuard({} as any, {} as any);

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should deny access when user data is invalid', () => {
    // Arrange
    localStorage.setItem('user', 'invalid-json');

    // Act
    const result = executeGuard({} as any, {} as any);

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should deny access when user data is null', () => {
    // Arrange
    localStorage.setItem('user', 'null');

    // Act
    const result = executeGuard({} as any, {} as any);

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should deny access when localStorage is empty', () => {
    // Act
    const result = executeGuard({} as any, {} as any);

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });
});
