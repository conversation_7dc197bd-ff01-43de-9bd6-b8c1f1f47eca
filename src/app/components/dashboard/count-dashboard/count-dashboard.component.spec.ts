import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CountDashboardComponent } from './count-dashboard.component';
import { CommonModule } from '@angular/common';

describe('CountDashboardComponent', () => {
  let component: CountDashboardComponent;
  let fixture: ComponentFixture<CountDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CountDashboardComponent, CommonModule]
    }).compileComponents();

    fixture = TestBed.createComponent(CountDashboardComponent);
    component = fixture.componentInstance;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Properties', () => {
    it('should have default chartInitializedStatus as false', () => {
      expect(component.chartInitializedStatus).toBe(false);
    });

    it('should accept icon input', () => {
      const testIcon = 'assets/img/icons/test.png';
      component.icon = testIcon;
      expect(component.icon).toBe(testIcon);
    });

    it('should accept title input', () => {
      const testTitle = 'Test Title';
      component.title = testTitle;
      expect(component.title).toBe(testTitle);
    });

    it('should accept count input', () => {
      const testCount = 150;
      component.count = testCount;
      expect(component.count).toBe(testCount);
    });

    it('should accept total input', () => {
      const testTotal = 1000;
      component.total = testTotal;
      expect(component.total).toBe(testTotal);
    });

    it('should accept color input', () => {
      const testColor = '#FF5733';
      component.color = testColor;
      expect(component.color).toBe(testColor);
    });
  });

  describe('progress getter', () => {
    it('should calculate progress percentage correctly', () => {
      component.count = 250;
      component.total = 1000;
      expect(component.progress).toBe(25.0);
    });

    it('should handle zero total', () => {
      component.count = 100;
      component.total = 0;
      expect(component.progress).toBe(Infinity);
    });

    it('should handle zero count', () => {
      component.count = 0;
      component.total = 1000;
      expect(component.progress).toBe(0);
    });

    it('should scale down progress when over 100%', () => {
      component.count = 1500;
      component.total = 1000;
      const expectedProgress = (1500 / 1000) * 100 / 10; // 15.0
      expect(component.progress).toBe(15.0);
    });

    it('should handle exact 100% progress', () => {
      component.count = 1000;
      component.total = 1000;
      expect(component.progress).toBe(100.0);
    });

    it('should round to one decimal place', () => {
      component.count = 333;
      component.total = 1000;
      expect(component.progress).toBe(33.3);
    });

    it('should handle very small percentages', () => {
      component.count = 1;
      component.total = 10000;
      expect(component.progress).toBe(0.0);
    });

    it('should handle large numbers', () => {
      component.count = 999999;
      component.total = 1000000;
      expect(component.progress).toBe(100.0);
    });
  });

  describe('ngOnInit', () => {
    it('should set chartInitializedStatus to true', () => {
      component.ngOnInit();
      expect(component.chartInitializedStatus).toBe(true);
    });
  });

  describe('Template Rendering', () => {
    beforeEach(() => {
      component.icon = 'assets/img/icons/user.png';
      component.title = 'Users';
      component.count = 150;
      component.total = 1000;
      component.color = '#522ACC';
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should display the card when chartInitializedStatus is true', () => {
      const cardElement = fixture.nativeElement.querySelector('.card');
      expect(cardElement).toBeTruthy();
    });

    it('should display the loading spinner when chartInitializedStatus is false', () => {
      component.chartInitializedStatus = false;
      fixture.detectChanges();

      const loadingElement = fixture.nativeElement.querySelector('.loading-container');
      expect(loadingElement).toBeTruthy();
    });

    it('should display the correct title', () => {
      const titleElement = fixture.nativeElement.querySelector('h3');
      expect(titleElement.textContent).toBe('Users');
    });

    it('should display the correct count', () => {
      const countElement = fixture.nativeElement.querySelector('p');
      expect(countElement.textContent.trim()).toBe('150');
    });
  });

  describe('Edge Cases', () => {
    it('should handle negative count values', () => {
      component.count = -50;
      component.total = 1000;
      expect(component.progress).toBe(-5.0);
    });

    it('should handle negative total values', () => {
      component.count = 100;
      component.total = -1000;
      expect(component.progress).toBe(-10.0);
    });

    it('should handle decimal count values', () => {
      component.count = 150.5;
      component.total = 1000;
      expect(component.progress).toBe(15.1);
    });

    it('should handle decimal total values', () => {
      component.count = 150;
      component.total = 999.9;
      expect(component.progress).toBe(15.0);
    });
  });

  describe('Input Changes', () => {
    it('should update progress when count changes', () => {
      component.count = 100;
      component.total = 1000;
      expect(component.progress).toBe(10.0);

      component.count = 200;
      expect(component.progress).toBe(20.0);
    });

    it('should update progress when total changes', () => {
      component.count = 100;
      component.total = 1000;
      expect(component.progress).toBe(10.0);

      component.total = 500;
      expect(component.progress).toBe(20.0);
    });
  });
});
