import { FormControl } from '@angular/forms';
import { adaniDomainValidator } from './adani-email-validator';

describe('adaniDomainValidator', () => {
  let validator: any;

  beforeEach(() => {
    validator = adaniDomainValidator();
  });

  it('should be created', () => {
    expect(validator).toBeTruthy();
  });

  it('should return null for empty value', () => {
    // Arrange
    const control = new FormControl('');

    // Act
    const result = validator(control);

    // Assert
    expect(result).toBeNull();
  });

  it('should return null for null value', () => {
    // Arrange
    const control = new FormControl(null);

    // Act
    const result = validator(control);

    // Assert
    expect(result).toBeNull();
  });

  it('should return null for undefined value', () => {
    // Arrange
    const control = new FormControl(undefined);

    // Act
    const result = validator(control);

    // Assert
    expect(result).toBeNull();
  });

  it('should return null for valid adani email', () => {
    // Arrange
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    validEmails.forEach(email => {
      const control = new FormControl(email);

      // Act
      const result = validator(control);

      // Assert
      expect(result).toBeNull();
    });
  });



  it('should return error for invalid email format', () => {
    // Test each email individually
    const invalidEmails = [
      'invalid-email',
      '@adani.com',
      'test@',
      'test.adani.com',
      'test@@adani.com',
      '<EMAIL>.',
      '.<EMAIL>',
      '<EMAIL>'
    ];

    invalidEmails.forEach(email => {
      const control = new FormControl(email);
      const result = validator(control);
      expect(result).toEqual({ adaniDomain: true });
    });
  });

  it('should return error for emails with valid format but invalid domain', () => {
    // Test emails that have valid format but wrong domain
    const invalidDomainEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    invalidDomainEmails.forEach(email => {
      const control = new FormControl(email);
      const result = validator(control);
      expect(result).toEqual({ adaniDomain: true });
    });
  });

  it('should return error for local part without alphabets', () => {
    // Arrange
    const invalidLocalPartEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '...@adani.com',
      '+++@adani.com'
    ];

    invalidLocalPartEmails.forEach(email => {
      const control = new FormControl(email);

      // Act
      const result = validator(control);

      // Assert
      expect(result).toEqual({ adaniDomain: true });
    });
  });

  it('should return null for local part with mixed characters including alphabets', () => {
    // Arrange
    const validLocalPartEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    validLocalPartEmails.forEach(email => {
      const control = new FormControl(email);

      // Act
      const result = validator(control);

      // Assert
      expect(result).toBeNull();
    });
  });

  it('should handle edge cases', () => {
    // Arrange & Act & Assert
    const edgeCases = [
      { email: '<EMAIL>', expected: null }, // Minimum valid
      { email: '<EMAIL>', expected: null }, // Long but valid
      { email: '<EMAIL>', expected: { adaniDomain: true } }, // Case sensitive
      { email: 'test @adani.com', expected: { adaniDomain: true } }, // Space in local part
      { email: 'test@adani .com', expected: { adaniDomain: true } }, // Space in domain
    ];

    edgeCases.forEach(testCase => {
      const control = new FormControl(testCase.email);
      const result = validator(control);
      expect(result).toEqual(testCase.expected);
    });
  });
});
