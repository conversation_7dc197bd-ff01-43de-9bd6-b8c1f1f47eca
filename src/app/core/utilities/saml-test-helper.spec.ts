import { generateMockSamlToken, createMockSamlCallbackUrl, isValidAdaniEmail } from './saml-test-helper';

describe('SAML Test Helper', () => {
  describe('generateMockSamlToken', () => {
    it('should generate a mock SAML token', () => {
      // Act
      const token = generateMockSamlToken();

      // Assert
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token).toMatch(/^mock-saml-token-\d+$/);
    });

    it('should generate unique tokens', () => {
      // Act
      const token1 = generateMockSamlToken();
      const token2 = generateMockSamlToken();

      // Assert
      expect(token1).not.toBe(token2);
    });

    it('should include timestamp in token', () => {
      // Arrange
      const beforeTime = Date.now();
      
      // Act
      const token = generateMockSamlToken();
      
      // Arrange
      const afterTime = Date.now();
      const tokenTimestamp = parseInt(token.replace('mock-saml-token-', ''));

      // Assert
      expect(tokenTimestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(tokenTimestamp).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('createMockSamlCallbackUrl', () => {
    beforeEach(() => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: {
          origin: 'http://localhost:4200',
          pathname: '/app'
        },
        writable: true
      });
    });

    it('should create a mock SAML callback URL', () => {
      // Arrange
      const token = 'test-token-123';

      // Act
      const url = createMockSamlCallbackUrl(token);

      // Assert
      expect(url).toBeDefined();
      expect(typeof url).toBe('string');
      expect(url).toContain('http://localhost:4200/app');
      expect(url).toContain('redirect=');
      expect(url).toContain('callback?token=test-token-123');
    });

    it('should properly encode the callback URL', () => {
      // Arrange
      const token = 'test-token-with-special-chars!@#';

      // Act
      const url = createMockSamlCallbackUrl(token);

      // Assert
      expect(url).toContain(encodeURIComponent('callback?token=test-token-with-special-chars!@#'));
    });

    it('should handle different base URLs', () => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: {
          origin: 'https://example.com',
          pathname: '/different/path'
        },
        writable: true
      });
      const token = 'test-token';

      // Act
      const url = createMockSamlCallbackUrl(token);

      // Assert
      expect(url).toContain('https://example.com/different/path');
      expect(url).toContain('redirect=');
      expect(url).toContain('callback?token=test-token');
    });

    it('should handle empty pathname', () => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: {
          origin: 'https://example.com',
          pathname: ''
        },
        writable: true
      });
      const token = 'test-token';

      // Act
      const url = createMockSamlCallbackUrl(token);

      // Assert
      expect(url).toBe('https://example.com?redirect=callback%3Ftoken%3Dtest-token');
    });
  });

  describe('isValidAdaniEmail', () => {
    it('should return true for valid Adani emails', () => {
      // Arrange
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      // Act & Assert
      validEmails.forEach(email => {
        expect(isValidAdaniEmail(email)).toBe(true);
      });
    });

    it('should return false for invalid Adani emails', () => {
      // Arrange
      const invalidEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        'test@adanicom',
        '@adani.com',
        'test@',
        'invalid-email',
        'test.adani.com',
        ''
      ];

      // Act & Assert
      invalidEmails.forEach(email => {
        expect(isValidAdaniEmail(email)).toBe(false);
      });
    });

    it('should return false for emails without @ symbol', () => {
      // Arrange
      const emailsWithoutAt = [
        'testadani.com',
        'useradanicom',
        'admin.adani.com',
        'test'
      ];

      // Act & Assert
      emailsWithoutAt.forEach(email => {
        expect(isValidAdaniEmail(email)).toBe(false);
      });
    });

    it('should return false for empty or null inputs', () => {
      // Act & Assert
      expect(isValidAdaniEmail('')).toBe(false);
      expect(isValidAdaniEmail(null as any)).toBe(false);
      expect(isValidAdaniEmail(undefined as any)).toBe(false);
    });

    it('should be case sensitive for domain', () => {
      // Arrange
      const caseVariations = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      // Act & Assert
      caseVariations.forEach(email => {
        expect(isValidAdaniEmail(email)).toBe(false);
      });
    });

    it('should handle special characters in local part', () => {
      // Arrange
      const specialCharEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      // Act & Assert
      specialCharEmails.forEach(email => {
        expect(isValidAdaniEmail(email)).toBe(true);
      });
    });
  });
});
