import { TestBed } from '@angular/core/testing';
import { AuthService } from './auth.service';
import { ApiService } from './api.service';

describe('AuthService', () => {
  let service: AuthService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockUser = {
    id: 1,
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    businessUnitId: 1,
    adminsRole: { id: 1, name: 'Admin' },
    plantIds: [1, 2],
    plant: [{ id: 1, name: 'Plant 1' }]
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['postData']);

    TestBed.configureTestingModule({
      providers: [
        AuthService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(AuthService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    // Clear console spies
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('sendOtp', () => {
    it('should send OTP successfully', async () => {
      // Arrange
      const credentials = { email: '<EMAIL>' };
      const mockResponse = { responseCode: 200, message: 'OTP sent successfully' };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.sendOtp(credentials);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), credentials);
      expect(result).toEqual(mockResponse);
    });

    it('should handle OTP send failure', async () => {
      // Arrange
      const credentials = { email: '<EMAIL>' };
      const mockError = new Error('Email not found');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.sendOtp(credentials)).toBeRejectedWith(mockError);
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully and store tokens', async () => {
      // Arrange
      const otpData = { email: '<EMAIL>', otp: '123456' };
      const mockResponse = {
        responseCode: 200,
        accessToken: 'mock-access-token',
        user: mockUser,
        message: 'OTP verified successfully'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.verifyOtp(otpData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), otpData);
      expect(result).toEqual(mockResponse);
      expect(localStorage.getItem('token')).toBe('mock-access-token');
      expect(localStorage.getItem('user')).toBe(JSON.stringify(mockUser));
    });

    it('should handle invalid OTP', async () => {
      // Arrange
      const otpData = { email: '<EMAIL>', otp: '000000' };
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid OTP'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.verifyOtp(otpData);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(localStorage.getItem('token')).toBeNull();
      expect(localStorage.getItem('user')).toBeNull();
    });

    it('should handle OTP verification error', async () => {
      // Arrange
      const otpData = { email: '<EMAIL>', otp: '123456' };
      const mockError = new Error('Network Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.verifyOtp(otpData)).toBeRejectedWith(mockError);
    });
  });

  describe('signup', () => {
    it('should signup successfully', async () => {
      // Arrange
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        businessUnitId: 1
      };
      const mockResponse = { responseCode: 200, message: 'User created successfully' };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.signup(userData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), userData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle signup failure', async () => {
      // Arrange
      const userData = { email: '<EMAIL>' };
      const mockError = new Error('Validation failed');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.signup(userData)).toBeRejectedWith(mockError);
    });
  });

  describe('logout', () => {
    it('should clear localStorage on logout', () => {
      // Arrange
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('token', 'mock-token');

      // Act
      service.logout();

      // Assert
      expect(localStorage.getItem('user')).toBeNull();
      expect(localStorage.getItem('token')).toBeNull();
    });

    it('should handle logout when storage is already empty', () => {
      // Act
      service.logout();

      // Assert
      expect(localStorage.getItem('user')).toBeNull();
      expect(localStorage.getItem('token')).toBeNull();
    });
  });

  describe('getBusinessUnitId', () => {
    it('should return businessUnitId from localStorage', () => {
      // Arrange
      localStorage.setItem('user', JSON.stringify(mockUser));

      // Act
      const result = service.getBusinessUnitId();

      // Assert
      expect(result).toBe(String(mockUser.businessUnitId));
    });

    it('should return null when no user in localStorage', () => {
      // Act
      const result = service.getBusinessUnitId();

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when user has no businessUnitId', () => {
      // Arrange
      const userWithoutBusinessUnit = { ...mockUser };
      delete (userWithoutBusinessUnit as any).businessUnitId;
      localStorage.setItem('user', JSON.stringify(userWithoutBusinessUnit));

      // Act
      const result = service.getBusinessUnitId();

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('SAML functionality', () => {
    let mockLocation: any;

    beforeEach(() => {
      // Create a mock location object
      mockLocation = { href: '' };
      // Replace window.location with our mock
      (window as any).location = mockLocation;
    });

    it('should initiate SSO', () => {
      // Act
      service.initiateSSO();

      // Assert
      expect(window.location.href).toContain('/saml/login');
      expect(console.log).toHaveBeenCalledWith('Initiating SAML SSO to:', jasmine.any(String));
    });

    it('should verify SAML token successfully', async () => {
      // Arrange
      const tokenData = { token: 'saml-token-123' };
      const mockResponse = {
        responseCode: 200,
        accessToken: 'saml-access-token',
        user: mockUser
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.verifySamlToken(tokenData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), tokenData);
      expect(result).toEqual(mockResponse);
      expect(localStorage.getItem('token')).toBe('saml-access-token');
      expect(localStorage.getItem('user')).toBe(JSON.stringify(mockUser));
    });

    it('should handle SAML token verification failure', async () => {
      // Arrange
      const tokenData = { token: 'invalid-token' };
      const mockError = new Error('Invalid SAML token');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.verifySamlToken(tokenData)).toBeRejectedWith(mockError);
      expect(console.error).toHaveBeenCalledWith('SAML token verification failed:', mockError);
    });

    it('should check email and role', async () => {
      // Arrange
      const emailData = { email: '<EMAIL>' };
      const mockResponse = { exists: true, hasRole: true };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.checkEmailAndRole(emailData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), emailData);
      expect(result).toEqual(mockResponse);
    });
  });
});
